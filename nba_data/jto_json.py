import json

def txt_to_json(input_file, output_file):
    data = []
    
    with open(input_file, 'r') as f:
        # 读取第一行作为表头
        headers = f.readline().strip().split('\t')
        
        # 逐行读取数据
        for line in f:
            if line.strip():  # 跳过空行
                values = line.strip().split('\t')
                entry = dict(zip(headers, values))
                data.append(entry)
    
    # 写入 JSON 文件
    with open(output_file, 'w') as f:
        json.dump(data, f, indent=4)  # indent=4 使 JSON 格式化输出

# 示例调用
input_txt = "/Users/<USER>/Desktop/ZipCodestoDMAs.txt"
output_json = "output.json"
txt_to_json(input_txt, output_json)

print(f"JSON 文件已生成: {output_json}")