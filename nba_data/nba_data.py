import requests

headers = {
    'accept': '*/*',
    'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8',
    'origin': 'https://www.nba.com',
    'referer': 'https://www.nba.com/',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
}

response = requests.get('https://cdn.nba.com/static/json/liveData/scoreboard/todaysScoreboard_00.json', headers=headers)
#print(response.text)

res = response.json()
scoreboard = res.get("scoreboard")
gameDate = scoreboard.get("gameDate")
leagueId = scoreboard.get("leagueId")
leagueName = scoreboard.get("leagueName")

games = scoreboard.get("games")

for game in games:

    gameId = game.get("gameId")
    gameStatus = game.get("gameStatus") #Game 2
    gameStatusText = game.get("gameStatusText")
    gameClock = game.get("gameClock") #比赛进行时间
    gameTimeUTC = game.get("gameTimeUTC")
    gameEt = game.get("gameEt")
    regulationPeriods = game.get("regulationPeriods")
    seriesGameNumber = game.get("seriesGameNumber")
    gameLabel = game.get("gameLabel")
    gameSubLabel = game.get("gameSubLabel") 
    seriesText = game.get("seriesText")
    seriesConference = game.get("seriesConference")
    poRoundDesc = game.get("poRoundDesc")

    homeTeamId = game.get("homeTeam").get("teamId")
    homeTeamName = game.get("homeTeam").get("teamName")
    homeTeamCity = game.get("homeTeam").get("teamCity")
    homeTeamTricode = game.get("homeTeam").get("teamTricode")
    homeTeamWins = game.get("homeTeam").get("wins")
    homeTeamWLosses = game.get("homeTeam").get("losses")
    homeTeamScore = game.get("homeTeam").get("score")
    homeTeamTimeoutsRemaining = game.get("homeTeam").get("timeoutsRemaining")

    homeTeamPeriods = game.get("homeTeam").get("periods")

    for homeTeamPeriod in homeTeamPeriods:

        #print(f"Period {homeTeamPeriod['period']}: {homeTeamPeriod['score']}")
        globals()[f"homeTeamPeriod{homeTeamPeriod['period']}"] = homeTeamPeriod['score']


    awayTeamId = game.get("awayTeam").get("teamId")
    awayTeamName = game.get("awayTeam").get("teamName")
    awayTeamCity = game.get("awayTeam").get("teamCity")
    awayTeamTricode = game.get("awayTeam").get("teamTricode")
    awayTeamWins = game.get("awayTeam").get("wins")
    awayTeamWLosses = game.get("awayTeam").get("losses")
    awayTeamScore = game.get("awayTeam").get("score")
    awayTeamTimeoutsRemaining = game.get("awayTeam").get("timeoutsRemaining")

    awayTeamPeriods = game.get("awayTeam").get("periods")

    for awayTeamPeriod in awayTeamPeriods:

        #print(f"Period {awayTeamPeriod['period']}: {awayTeamPeriod['score']}")
        globals()[f"awayTeamPeriod{awayTeamPeriod['period']}"] = awayTeamPeriod['score']



homeTeamMVP = game.get("gameLeaders").get("homeLeaders")
homeTeamMVPid = game.get("gameLeaders").get("homeLeaders").get("personId")
homeTeamMVPName = game.get("gameLeaders").get("homeLeaders").get("name")
homeTeamMVPjerseyNum = game.get("gameLeaders").get("homeLeaders").get("jerseyNum")
homeTeamMVPposition = game.get("gameLeaders").get("homeLeaders").get("position")
homeTeamMVPpoints = game.get("gameLeaders").get("homeLeaders").get("points")
homeTeamMVPrebounds = game.get("gameLeaders").get("homeLeaders").get("rebounds")
homeTeamMVPassists = game.get("gameLeaders").get("homeLeaders").get("assists")

awayTeamMVP = game.get("gameLeaders").get("awayLeaders")
awayTeamMVPid = game.get("gameLeaders").get("awayLeaders").get("personId")
awayTeamMVPName = game.get("gameLeaders").get("awayLeaders").get("name")
awayTeamMVPjerseyNum = game.get("gameLeaders").get("awayLeaders").get("jerseyNum")
awayTeamMVPposition = game.get("gameLeaders").get("awayLeaders").get("position")
awayTeamMVPpoints = game.get("gameLeaders").get("awayLeaders").get("points")
awayTeamMVPrebounds = game.get("gameLeaders").get("awayLeaders").get("rebounds")
awayTeamMVPassists = game.get("gameLeaders").get("awayLeaders").get("assists")


print(gameLabel)
print(poRoundDesc)
print(gameSubLabel)
print(seriesText)
print(f"{awayTeamCity} {awayTeamName} @ {homeTeamCity} {homeTeamName}")
print(gameStatusText)
print(gameClock)

homeTeamScore

print(f"Game Score: {awayTeamTricode} {awayTeamScore} - {homeTeamTricode} {homeTeamScore}")

print(f"Period 1: {awayTeamTricode} {awayTeamPeriod1} - {homeTeamTricode} {homeTeamPeriod1}")
print(f"Period 2: {awayTeamTricode} {awayTeamPeriod2} - {homeTeamTricode} {homeTeamPeriod2}")
print(f"Period 3: {awayTeamTricode} {awayTeamPeriod3} - {homeTeamTricode} {homeTeamPeriod3}")
print(f"Period 4: {awayTeamTricode} {awayTeamPeriod4} - {homeTeamTricode} {homeTeamPeriod4}")

print()
print(f"Home Team MVP Name: {homeTeamMVPName}")
print(f"Home Team MVP Jesrsey Number: {homeTeamMVPjerseyNum}")
print(f"Home Team MVP Position: {awayTeamMVPposition}")
print(f"Home Team MVP Points: {homeTeamMVPpoints}")
print(f"Home Team MVP Rebounds: {awayTeamMVPrebounds}")
print(f"Home Team MVP Assists: {homeTeamMVPassists}")
print()
print(f"Away Team MVP Name: {awayTeamMVPName}")
print(f"Away Team MVP Jesrsey Number: {awayTeamMVPjerseyNum}")
print(f"Away Team MVP Position: {awayTeamMVPposition}")
print(f"Away Team MVP Points: {awayTeamMVPpoints}")
print(f"Away Team MVP Rebounds: {awayTeamMVPrebounds}")
print(f"Away Team MVP Assists: {awayTeamMVPassists}")
