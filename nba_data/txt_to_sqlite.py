import sqlite3

def txt_to_sqlite(input_file, output_db):
    # 连接到 SQLite 数据库（如果不存在会自动创建）
    conn = sqlite3.connect(output_db)
    cursor = conn.cursor()
    
    # 创建表（假设表名为 `dma_data`）
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS dma_data (
            zip_code TEXT,
            dma_code INTEGER,
            dma_description TEXT
        )
    ''')
    
    # 读取 txt 文件并插入数据
    with open(input_file, 'r') as f:
        # 跳过表头（第一行）
        headers = f.readline().strip().split('\t')
        
        # 逐行插入数据
        for line in f:
            if line.strip():  # 跳过空行
                zip_code, dma_code, dma_description = line.strip().split('\t')
                cursor.execute(
                    "INSERT INTO dma_data (zip_code, dma_code, dma_description) VALUES (?, ?, ?)",
                    (zip_code, dma_code, dma_description)
                )
    
    # 提交更改并关闭连接
    conn.commit()
    conn.close()

# 示例调用
input_txt = "/Users/<USER>/Desktop/ZipCodestoDMAs.txt"
output_db = "output.db"
txt_to_sqlite(input_txt, output_db)

print(f"SQLite 数据库已生成: {output_db}")