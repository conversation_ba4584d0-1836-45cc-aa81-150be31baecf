import requests

headers = {
    'origin': 'https://www.nba.com',
    'referer': 'https://www.nba.com/',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
}

response = requests.get('https://cdn.nba.com/static/json/staticData/scheduleLeagueV2_1.json', headers=headers)
#print(response.text)

res = response.json()

seasonYear = res.get("leagueSchedule").get("seasonYear")

gameDates =  res.get("leagueSchedule").get("gameDates")

for gameDate in gameDates:

    games = gameDate.get("games")#.get("gameId")

    for game in games:

        gameId = game.get("gameId")
        gameCode = game.get("gameCode")
        gameDateEst = game.get("gameDateEst")
        gameDateTimeEst = game.get("gameDateTimeEst")
        gameTimeEst = game.get("gameTimeEst")
        day = game.get("day")

        gameLabel = game.get("gameLabel")
        gameSubLabel = game.get("gameSubLabel")
        seriesText = game.get("seriesText")
        arenaName = game.get("arenaName")
        arenaCity = game.get("arenaCity")
        day = game.get("day")

        hometeamId = game.get("homeTeam").get("teamId")
        hometeamName = game.get("homeTeam").get("teamName")
        hometeamCity = game.get("homeTeam").get("teamCity")
        hometeamTricode = game.get("homeTeam").get("teamTricode")
        hometeamSlug = game.get("homeTeam").get("teamSlug")
        hometeamCity = game.get("homeTeam").get("teamCity")

        awayteamId = game.get("awayTeam").get("teamId")
        awayteamName = game.get("awayTeam").get("teamName")
        awayteamCity = game.get("awayTeam").get("teamCity")
        awayteamTricode = game.get("awayTeam").get("teamTricode")
        awayteamSlug = game.get("awayTeam").get("teamSlug")
        awayteamCity = game.get("awayTeam").get("teamCity")



        print('####### start ######')
        print('gameId' + gameId)
        print(f"{awayteamCity} {awayteamName} @ {hometeamCity} {hometeamName}")
        print(gameDateTimeEst)
        print(day)
        print(arenaCity)
        print(gameLabel)
        print(gameSubLabel)
        print('####### end ######')
        print()
        print()
        print()
