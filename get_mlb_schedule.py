from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import pandas as pd
import time

def get_mlb_schedule():
    # 配置浏览器选项
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 无头模式
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    
    # 初始化浏览器
    driver = webdriver.Chrome(
        service=Service(ChromeDriverManager().install()),
        options=chrome_options
    )
    
    try:
        # 访问目标URL
        driver.get("https://www.cbssports.com/mlb/schedule/")
        
        # 等待动态内容加载（关键）
        time.sleep(5)  # 可根据网络情况调整
        
        # 获取页面源码
        html = driver.page_source
        soup = BeautifulSoup(html, 'html.parser')
        
        # 定位赛程表格
        schedule_table = soup.find('table', {'class': 'TableBase-table'})
        if not schedule_table:
            raise ValueError("未找到赛程表格，网站结构可能已变更")
        
        # 解析数据
        games = []
        for row in schedule_table.find_all('tr')[1:]:  # 跳过表头
            cols = row.find_all('td')
            if len(cols) >= 7:  # 确保是有效数据行
                game = {
                    'date': cols[0].get_text(strip=True),
                    'away_team': cols[1].get_text(strip=True),
                    'home_team': cols[2].get_text(strip=True),
                    'score': cols[3].get_text(strip=True),
                    'win_pitcher': cols[4].get_text(strip=True),
                    'loss_pitcher': cols[5].get_text(strip=True),
                    'save_pitcher': cols[6].get_text(strip=True) if len(cols)>6 else 'N/A'
                }
                games.append(game)
        
        return pd.DataFrame(games)
        
    except Exception as e:
        print(f"抓取失败: {str(e)}")
        return None
    finally:
        driver.quit()

# 使用示例
if __name__ == "__main__":
    schedule_df = get_mlb_schedule()
    if schedule_df is not None:
        print(schedule_df.head())
        # 保存到CSV
        schedule_df.to_csv('mlb_schedule.csv', index=False)